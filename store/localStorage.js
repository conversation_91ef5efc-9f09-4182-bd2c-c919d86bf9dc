import responseHandler from '~/helpers/responseHandler'

export const state = () => ({
  currentLocale: 'en',
  userData: null,
  userToken: null,
  errorCode: null,
  regions: [],
  types: [],
  closes: [],
  branch_user: null,
  fcm_token: null,
  notifications: null,
  unread_notifications: 0,
  dashboard: null,
  expire: 24,
  status: true,
})

export const mutations = {
  SET_CURRENT_LOCALE(state, payload) {
    state.currentLocale = payload
  },
  SET_USER_TOKEN(state, payload) {
    state.userToken = payload
  },
  SET_USER_DATA(state, payload) {
    state.userData = payload
  },
  SET_ERROR_MSG(state, payload) {
    state.errorCode = payload
  },
  SET_REGIONS(state, payload) {
    state.regions = payload
  },
  SET_TYPES(state, payload) {
    state.types = payload
  },
  SET_CLOSES(state, payload) {
    state.closes = payload
  },
  SET_HOME_PAGE(state, payload) {
    state.dashboard = payload
  },
  SET_BRANCH_USER(state, payload) {
    state.branch_user = payload
  },
  SET_NOTIFICATIONS(state, payload) {
    state.notifications = payload
  },
  PUSH_NOTIFICATION(state, payload) {
    if (state.notifications) {
      // Create a new array to ensure Vue reactivity
      state.notifications = [payload, ...state.notifications]
    } else {
      // Initialize notifications array if it doesn't exist
      state.notifications = [payload]
    }
  },
  SET_UNREAD_NOTIFICATION(state, payload) {
    state.unread_notifications = payload
  },
  SET_NOTIFICATIONS_AS_READ(state) {
    state.notifications.forEach((elem) => {
      if (elem.readed == false) {
        elem.readed = true
      }
    })
  },
  SET_NOTIFICATION_AS_READ(state, payload) {
    state.notifications.forEach((elem) => {
      if (elem.id == payload) {
        if (elem.readed == false) {
          elem.readed = true
        }
      }
    })
  },
  EMPTY_NOTIFICATION(state) {
    state.notifications = []
  },
  SET_FCM_TOKEN(state, payload) {
    state.fcm_token = payload
  },
  Login_As(state, payload) {
    state.branch_user = payload
  },
  RESET_USER(st) {
    const states = (({ userToken, userData }) => ({
      userToken,
      userData,
    }))(state())

    Object.keys(states).forEach((key) => {
      st[key] = states[key]
    })
    this.$cookies.remove('userToken')
    this.$cookies.remove('userData')
  },
  RESET_BRANCH_USER(st) {
    const states = (({ branch_user }) => ({
      branch_user,
    }))(state())

    Object.keys(states).forEach((key) => {
      st[key] = states[key]
    })
    this.$cookies.remove('loginAs')
  },
  SET_STORE_EXPIRE(state, payload) {
    state.expire = payload
  },
}

export const getters = {
  currentLocale(state) {
    return state.currentLocale
  },
  userToken(state) {
    return state.userToken
  },
  userData(state) {
    return state.userData
  },
  get_error_code(state) {
    return state.errorCode
  },
  get_regions(state) {
    let list = []
    const original_array = state.regions
    original_array.forEach((elem) => {
      list.push({
        name: elem.name,
        value: elem.id,
      })
    })
    return list
  },
  get_types(state) {
    let list = []
    const original_array = state.types
    original_array.forEach((elem) => {
      list.push({
        name: elem.name,
        value: elem.id,
      })
    })
    return list
  },
  get_closes(state) {
    let list = []
    const original_array = state.closes
    original_array.forEach((elem) => {
      list.push({
        name: elem.name,
        value: elem.id,
      })
    })
    return list
  },
  get_fcm(state) {
    return state.fcm_token
  },
  get_notifications(state) {
    return state.notifications
  },
  get_unread_notifications(state) {
    return state.unread_notifications
  },
  get_branch_user(state) {
    return state.branch_user
  },
  get_dashboard(state) {
    return state.dashboard
  },
}

export const actions = {
  response_handler({ commit }, payload) {
    responseHandler(payload, { commit })
  },
}
