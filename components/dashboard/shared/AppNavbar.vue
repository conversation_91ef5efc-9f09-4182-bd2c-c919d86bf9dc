<template>
  <client-only>
    <div>
      <nav class="d-app_navbar">
        <div class="welcome_wrapper" v-if="userData != null">
          <h4>{{ $t('admin.hello') }} {{ userData.company_name + '👋🏻' }}</h4>
        </div>
        <!-- end::welcome_wrapper -->

        <div class="options_wrapper">
          <div class="item bordered">
            <a href="javascript:;" @click.prevent="handleCreateBid" class="btn btn-default">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#plus"></use>
              </svg>
              <span> {{ $t('admin.create_bid') }}</span>
            </a>
          </div>
          <!-- end::item -->
          <div class="item">
            <b-dropdown class="lang_switcher" no-caret>
              <template #button-content>
                <img :src="require(`~/static/${$i18n.locale}.svg`)" alt="flag" />
                <span v-if="$i18n.locale == 'en'">En</span>
                <span v-if="$i18n.locale == 'ar'">Ar</span>
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#angle-down"></use>
                </svg>
              </template>
              <b-dropdown-item @click="switchMyLang('en')">
                English
              </b-dropdown-item>
              <b-dropdown-item @click="switchMyLang('ar')">
                العربية
              </b-dropdown-item>
            </b-dropdown>
          </div>
          <!-- end::item -->
          <div class="item">
            <button type="button" class="btn btn-default notification" v-b-toggle.notifications>
              <svg class="icon" @click="refetchNotification">
                <use xlink:href="~/static/sprite.svg#notifications"></use>
              </svg>
              <span class="badge">
                {{ filteredUnreadCount }}
              </span>
            </button>
          </div>
          <!-- end::item -->
          <div class="item">
            <button type="button" class="btn btn-default avatar" v-b-toggle.client_info v-if="userData != null">
              <img :src="userData.logo" alt="avatar" />
            </button>
          </div>
          <!-- end::item -->
        </div>
        <!-- end::options_wrapper -->
      </nav>

      <nav class="res_navabr_dahsboard">
        <div class="top_wrapper">
          <div class="row">
            <div class="col-6">
              <div class="logo_wrapper">
                <nuxt-link :to="localePath('/dashboard')">
                  <img src="~/static/logo.svg" alt="logo" />
                </nuxt-link>
              </div>
            </div>
            <!-- end::col -->
            <div class="col-6">
              <div class="sidebar_trigger">
                <button type="button" class="btn btn-default" v-b-toggle.sidebar>
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#menu-burg"></use>
                  </svg>
                </button>
              </div>
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
        </div>
        <!-- end::top_wrapper -->

        <div class="bottom_wrapper">
          <div class="row">
            <div class="col-7">
              <div class="profile_wrapper">
                <a href="javascript:;" @click.prevent="handleCreateBid" class="btn btn-default">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#plus"></use>
                  </svg>
                  <span> {{ $t('admin.create_bid') }}</span>
                </a>
              </div>
            </div>
            <!-- end::col -->
            <div class="col-5">
              <div class="options_wrapper">
                <div class="item">
                  <b-dropdown class="lang_switcher" no-caret>
                    <template #button-content>
                      <img :src="require(`~/static/${$i18n.locale}.svg`)" alt="flag" />
                      <span v-if="$i18n.locale == 'en'">En</span>
                      <span v-if="$i18n.locale == 'ar'">Ar</span>
                      <svg class="icon">
                        <use xlink:href="~/static/sprite.svg#angle-down"></use>
                      </svg>
                    </template>
                    <b-dropdown-item @click="switchMyLang('en')">
                      English
                    </b-dropdown-item>
                    <b-dropdown-item @click="switchMyLang('ar')">
                      العربية
                    </b-dropdown-item>
                  </b-dropdown>
                </div>
                <!-- end::item -->
                <div class="item">
                  <button type="button" class="btn btn-default notification" v-b-toggle.notifications>
                    <svg class="icon" @click="refetchNotification">
                      <use xlink:href="~/static/sprite.svg#notifications"></use>
                    </svg>
                    <span class="badge">
                      {{ filteredUnreadCount }}
                    </span>
                  </button>
                </div>
                <!-- end::item -->
              </div>
              <!-- end::options_wrapper -->
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
        </div>
        <!-- end::bottom_wrapper -->
      </nav>

      <b-sidebar id="notifications" :title="$t('admin.notifications')" shadow>
        <div class="px-3 py-3">
          <div class="header_wrapper">
            <p class="count">
              {{ filteredUnreadCount }} {{ $t('admin.new_notifications') }}
            </p>
            <div class="header_actions">
              <button type="button" class="btn btn-sm btn-outline-primary filter-btn"
                @click="toggleNotificationFilters">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#menu-burg"></use>
                </svg>
                {{ $t('admin.filter') }}
              </button>
              <a href="javascript:;" @click="markAllAsRead">
                {{ $t('admin.mark_as_read') }}
              </a>
              <a href="javascript:;" style="color: #cc0000" @click="confirmDelete">
                {{ $t('admin.delete_notify') }}
              </a>
            </div>
          </div>
          <!-- end::header_wrapper -->

          <!-- Notification Filters Panel -->
          <div class="filters_wrapper" v-show="showNotificationFilters">
            <div class="filters_header">
              <h6>{{ $t('admin.notification_filters') }}</h6>
              <div class="filter_actions">
                <button type="button" class="btn btn-sm btn-link" @click="selectAllNotificationFilters">
                  {{ $t('admin.select_all') }}
                </button>
                <button type="button" class="btn btn-sm btn-link" @click="deselectAllNotificationFilters">
                  {{ $t('admin.deselect_all') }}
                </button>
              </div>
            </div>
            <div class="filters_body">
              <div class="filter_item" v-for="(label, type) in notificationTypeLabels" :key="type">
                <label class="checkbox_wrapper">
                  <input type="checkbox" :checked="notificationFilters[type]"
                    @change="toggleNotificationFilter(type)" />
                  <span class="checkmark"></span>
                  <span class="label_text">{{ label }}</span>
                </label>
              </div>
            </div>
          </div>
          <!-- end::filters_wrapper -->

          <div class="body_wrapper">
            <div class="wrapper" :class="{ unread: item.readed == false }"
              @click="redirectDetails(item.object_id, item.type, item.id)" v-for="(item, idx) in filteredNotifications"
              v-b-toggle.notifications :key="idx">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#act-create" v-if="
                    item.type == 'CreatedBid' ||
                    item.type == 'BidHasBeenUpdated'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#act-invite" v-if="
                    item.type == 'InvitedToBid' || item.type == 'BidInvite'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#act-submit" v-if="
                    item.type == 'SubmittedOffer' ||
                    item.type == 'NewOfferAdded' ||
                    item.type == 'OfferUpdated' ||
                    item.type == 'OfferUpdated'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#envelope" v-if="
                    item.type == 'NewCommentNotification'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#time" v-if="
                    item.type == 'biddingPhaseStarted' ||
                    item.type == 'biddingPhaseEnded'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#auction" v-if="
                    item.type == 'competitorBidUpdated'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#trash" v-if="
                    item.type == 'tenderDeleteRequested'
                  "></use>
                </svg>
              </div>
              <div class="info">
                <h6>{{ item.text }}</h6>
                <p class="date">{{ $moment(item.created_at).format('ll') }}</p>
              </div>
            </div>
            <!-- end::wrapper -->
          </div>
          <!-- end::body_wrapper -->
        </div>
      </b-sidebar>
    </div>
  </client-only>
</template>

<script>
// importing vuex tools
import { mapGetters } from 'vuex'
import { mapState } from 'vuex'

export default {
  name: 'DashboardNavbar',
  data() {
    return {
      eventSource: null,
      reconnectTimeout: null,
      showNotificationFilters: false,
      notificationFilters: {
        CreatedBid: true,
        BidHasBeenUpdated: true,
        InvitedToBid: true,
        BidInvite: true,
        SubmittedOffer: true,
        NewOfferAdded: true,
        OfferUpdated: true,
        NewCommentNotification: true,
        biddingPhaseStarted: true,
        competitorBidUpdated: true,
        tenderDeleteRequested: true,
        biddingPhaseEnded: true,
      },
    }
  },
  mounted() {
    if (this.$store.getters['localStorage/notifications'] == null) {
      this.$axios.$get('/client/auth/notifications').then((res) => {
        this.$store.commit(
          'localStorage/SET_NOTIFICATIONS',
          res.data.notifications
        )
        this.$store.commit(
          'localStorage/SET_UNREAD_NOTIFICATION',
          res.notificationsCount
        )
      })
    }

    // Load saved notification filters from localStorage
    const savedFilters = localStorage.getItem('notificationFilters')
    if (savedFilters) {
      try {
        this.notificationFilters = { ...this.notificationFilters, ...JSON.parse(savedFilters) }
      } catch (e) {
        console.warn('Failed to parse saved notification filters')
      }
    }

    this.connectToSSE()
  },
  beforeDestroy() {
    this.cleanupSSE()
  },
  watch: {
    // Watch for changes in notifications to ensure reactivity
    notifications: {
      handler() {
        // Force reactivity update when notifications change
        this.$forceUpdate()
      },
      deep: true
    },
    // Watch the computed property to debug
    filteredUnreadCount(newCount, oldCount) {
      console.log('filteredUnreadCount changed:', { oldCount, newCount, notifications: this.notifications?.length })
    }
  },
  computed: {
    ...mapGetters({
      notifications: ['localStorage/get_notifications'],
      unread: ['localStorage/get_unread_notifications'],
      userData: ['localStorage/userData'],
    }),
    ...mapState({
      homepage: (state) => state.localStorage.dashboard,
      userToken: (state) => state.localStorage.userToken,
    }),
    filteredNotifications() {
      if (!this.notifications) return []
      return this.notifications.filter(notification => {
        // If filter doesn't exist for this type, default to true (show it)
        const filterValue = this.notificationFilters[notification.type]
        return filterValue !== false
      })
    },
    filteredUnreadCount() {
      if (!this.notifications || !Array.isArray(this.notifications)) return 0

      // Explicitly depend on both notifications and filters for reactivity
      const notifications = this.notifications
      const filters = this.notificationFilters

      const count = notifications.filter(notification => {
        // Only count unread notifications that pass the filter
        if (notification.readed) return false

        // If filter doesn't exist for this type, default to true (show it)
        const filterValue = filters[notification.type]
        return filterValue !== false
      }).length

      console.log('filteredUnreadCount calculation:', {
        totalNotifications: notifications.length,
        unreadNotifications: notifications.filter(n => !n.readed).length,
        filteredUnreadCount: count,
        filters
      })

      return count
    },
    notificationTypeLabels() {
      return {
        CreatedBid: this.$t('admin.notification_type_created_bid'),
        BidHasBeenUpdated: this.$t('admin.notification_type_bid_updated'),
        InvitedToBid: this.$t('admin.notification_type_invited_bid'),
        BidInvite: this.$t('admin.notification_type_bid_invite'),
        SubmittedOffer: this.$t('admin.notification_type_submitted_offer'),
        NewOfferAdded: this.$t('admin.notification_type_new_offer'),
        OfferUpdated: this.$t('admin.notification_type_offer_updated'),
        NewCommentNotification: this.$t('admin.notifiction_type_comment_or_reply'),
        biddingPhaseStarted: this.$t('admin.notifiction_type_bidding_phase_started'),
        competitorBidUpdated: this.$t('admin.notifiction_type_competitor_bid_updated'),
        tenderDeleteRequested: this.$t('admin.notifiction_type_tender_delete_requested'),
        biddingPhaseEnded: this.$t('admin.notifiction_type_bidding_phase_ended'),
      }
    },
  },
  methods: {
    openNotifications() {
      this.$root.$emit('bv::toggle::sidebar', 'notifications')
      this.refetchNotification()
    },
    toggleNotificationFilters() {
      this.showNotificationFilters = !this.showNotificationFilters
    },
    toggleNotificationFilter(type) {
      this.notificationFilters[type] = !this.notificationFilters[type]
      // Save to localStorage for persistence
      localStorage.setItem('notificationFilters', JSON.stringify(this.notificationFilters))
    },
    selectAllNotificationFilters() {
      Object.keys(this.notificationFilters).forEach(key => {
        this.notificationFilters[key] = true
      })
      localStorage.setItem('notificationFilters', JSON.stringify(this.notificationFilters))
    },
    deselectAllNotificationFilters() {
      Object.keys(this.notificationFilters).forEach(key => {
        this.notificationFilters[key] = false
      })
      localStorage.setItem('notificationFilters', JSON.stringify(this.notificationFilters))
    },
    async refetchNotification() {
      await this.$axios.$get('/client/auth/notifications').then((res) => {
        this.$store.commit(
          'localStorage/SET_NOTIFICATIONS',
          res.data.notifications
        )
        this.$store.commit(
          'localStorage/SET_UNREAD_NOTIFICATION',
          res.notificationsCount
        )
      })
    },
    handleCreateBid() {
      if (this.homepage.insights.current_package.type != 'NONE') {
        this.$router.push(this.localePath('/dashboard/mybids/create'))
      } else {
        this.$swal({
          title: this.$t('admin.no_subscribe'),
          text: this.$t('admin.no_subscribe_desc'),
          icon: 'error',
          confirmButtonText: this.$t('admin.thanks'),
          confirmButtonColor: '#0F5296',
          showCancelButton: false,
        })
      }
    },
    switchMyLang(locale) {
      this.$store.commit('localStorage/SET_CURRENT_LOCALE', locale)
      import(`~/locales/${locale}`).then((module) => {
        this.$i18n.setLocaleMessage(locale, module.default)
        window.history.replaceState('', '', this.switchLocalePath(locale))
        this.$nuxt.$router.go()
      })
    },
    async redirectDetails(object_id, type, id) {
      await this.$axios
        .post(`/client/auth/read_notifications`, {
          notification: id,
        })
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.$store.commit('localStorage/SET_NOTIFICATION_AS_READ', id)
            this.$store.commit(
              'localStorage/SET_UNREAD_NOTIFICATION',
              res.data.notificationsCount
            )
            if (object_id != null) {
              this.$router.push(
                this.localePath({
                  name: 'dashboard-bids-id',
                  params: { id: object_id },
                })
              )
            }
          }
        })
    },
    async markAllAsRead() {
      await this.$axios
        .post(`/client/auth/read_notifications`, {
          notification: null,
        })
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.$store.commit('localStorage/SET_NOTIFICATIONS_AS_READ')
            this.$store.commit(
              'localStorage/SET_UNREAD_NOTIFICATION',
              res.data.notificationsCount
            )
          }
        })
    },
    confirmDelete() {
      this.$swal({
        title: this.$t('admin.delete_notify'),
        text: this.$t('admin.delete_notify_desc'),
        icon: 'success',
        confirmButtonText: this.$t('admin.cancel'),
        confirmButtonColor: '#1E805D',
        showDenyButton: true,
        denyButtonText: this.$t('admin.delete_notify'),
      }).then((result) => {
        if (result.isDenied) {
          this.deleteAll()
        }
      })
    },
    async deleteAll() {
      await this.$axios.delete('/client/auth/notification').then((res) => {
        if (res.data.errorCode == 0) {
          this.$store.commit('localStorage/EMPTY_NOTIFICATION')
          this.$store.commit(
            'localStorage/SET_UNREAD_NOTIFICATION',
            res.data.notificationsCount
          )
        }
      })
    },
    cleanupSSE() {
      // Clear any pending reconnection timeout
      if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout);
        this.reconnectTimeout = null;
      }

      // Close EventSource connection
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
    },
    // Method to force refresh the computed properties
    refreshNotificationCounts() {
      this.$forceUpdate();
      this.$nextTick(() => {
        console.log('Forced update - filteredUnreadCount:', this.filteredUnreadCount);
      });
    },
    // Test method to simulate a new notification (for debugging)
    testAddNotification() {
      const testNotification = {
        id: Date.now(),
        type: 'TestNotification',
        readed: false,
        object_id: 1,
        created_at: new Date().toISOString(),
        message: 'Test notification'
      };

      console.log('Adding test notification:', testNotification);
      this.$store.commit('localStorage/PUSH_NOTIFICATION', testNotification);
      this.refreshNotificationCounts();
    },
    connectToSSE() {
      // Check if userToken exists
      if (!this.userToken) {
        console.warn('No user token available for SSE connection');
        return;
      }

      // Clean up any existing connections and timeouts
      this.cleanupSSE();

      try {
        const token = this.userToken.replace('Bearer ', '');
        this.eventSource = new EventSource(`https://backend-api.munaqes.com/api/client/auth/notifications-sse?auth_token=${token}`);

        this.eventSource.addEventListener('unreadCount', (event) => {
          this.$store.commit('localStorage/SET_UNREAD_NOTIFICATION', event.data);
        });

        this.eventSource.addEventListener('notification', (event) => {
          const newNotification = JSON.parse(event.data);
          console.log('New notification received:', newNotification);

          // Ensure the notification type is in our filters
          if (!(newNotification.type in this.notificationFilters)) {
            console.log('Adding new notification type to filters:', newNotification.type);
            this.$set(this.notificationFilters, newNotification.type, true);
            // Save to localStorage
            localStorage.setItem('notificationFilters', JSON.stringify(this.notificationFilters));
          }

          // Add the new notification to the store
          this.$store.commit('localStorage/PUSH_NOTIFICATION', newNotification);

          // Force Vue to update reactivity
          this.refreshNotificationCounts();

          this.$nextTick(() => {
            console.log('After adding notification:', {
              totalNotifications: this.notifications?.length,
              filteredUnreadCount: this.filteredUnreadCount,
              notificationFilters: this.notificationFilters
            });

            // Update the unread count based on actual notifications
            const actualUnreadCount = this.notifications ?
              this.notifications.filter(n => !n.readed).length : 0;
            this.$store.commit('localStorage/SET_UNREAD_NOTIFICATION', actualUnreadCount);
          });
        });

        this.eventSource.onerror = (e) => {
          console.error('SSE connection error:', e);
          this.eventSource.close();
          this.eventSource = null;

          // Only reconnect if we still have a valid token and component is not destroyed
          if (this.userToken && !this._isDestroyed) {
            this.reconnectTimeout = setTimeout(() => {
              this.reconnectTimeout = null;
              this.connectToSSE();
            }, 5000);
          }
        };
      } catch (error) {
        console.error('Failed to create SSE connection:', error);
      }
    },

  },
}
</script>

<style lang="scss">
#notifications {
  background-color: #fff !important;
  width: 380px;

  .header_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .count {
      margin-bottom: 0;
      font-weight: 500;
      font-size: 15px;
    }

    .header_actions {
      display: flex;
      align-items: center;
      gap: 10px;

      .filter-btn {
        border-color: $base-color;
        color: $base-color;
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 4px;

        .icon {
          width: 14px;
          height: 14px;
          stroke: $base-color;
        }

        &:hover {
          background-color: $base-color;
          color: #fff;

          .icon {
            stroke: #fff;
          }
        }
      }

      a {
        font-size: 14px;
        display: block;
        text-align: center;
        font-weight: 500;
        color: $base-color;
      }
    }
  }

  .filters_wrapper {
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 15px 0;
    margin-bottom: 20px;

    .filters_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;

      h6 {
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 600;
        color: $black-text-color;
      }

      .filter_actions {
        display: flex;
        gap: 5px;

        .btn-link {
          font-size: 12px;
          color: $base-color;
          padding: 2px 6px;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .filters_body {
      .filter_item {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .checkbox_wrapper {
          display: flex;
          align-items: center;
          cursor: pointer;
          font-size: 14px;
          margin-bottom: 0;

          input[type="checkbox"] {
            margin: 0;
            margin-inline-end: 8px;
            width: 16px;
            height: 16px;
            accent-color: $base-color;
          }

          .label_text {
            color: $black-text-color;
            font-weight: 500;
          }

          &:hover .label_text {
            color: $base-color;
          }
        }
      }
    }
  }

  .body_wrapper {
    .wrapper {
      padding: 10px 15px;
      border-bottom: 1px solid #eee;
      display: flex;

      &:hover {
        cursor: pointer;

        .info {
          h6 {
            color: $base-color;
          }
        }
      }

      &:last-child {
        border-bottom: none;
      }

      &.unread {
        .info {

          h6,
          .date {
            font-weight: 600;
          }
        }
      }

      .icon_wrapper {
        width: 39px;
        height: 39px;
        border-radius: 6px;
        background-color: $base-color-opacity;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
          width: 22px;
          height: 22px;
          stroke: $base-color;
        }
      }

      .info {
        width: calc(100% - 49px);
        margin-inline-start: 10px;

        h6 {
          margin-bottom: 8px;
          font-size: 16px;
          font-weight: 400;
          text-transform: capitalize;
        }

        .date {
          text-align: end;
          margin-bottom: 0;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
